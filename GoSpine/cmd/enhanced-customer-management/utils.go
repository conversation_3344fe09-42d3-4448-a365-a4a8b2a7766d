package main

import (
	"encoding/json"
	"log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"gobackend-hvac-kratos/internal/customer"
)

// 🔍 Search Customers Handler
func (s *CustomerManagementServer) handleSearchCustomers(c *gin.Context) {
	var searchReq CustomerSearchRequest
	if err := c.ShouldBindJSON(&searchReq); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Set defaults
	if searchReq.Page <= 0 {
		searchReq.Page = 1
	}
	if searchReq.PageSize <= 0 {
		searchReq.PageSize = 20
	}
	if searchReq.SortBy == "" {
		searchReq.SortBy = "created_at"
	}
	if searchReq.SortOrder == "" {
		searchReq.SortOrder = "DESC"
	}

	offset := (searchReq.Page - 1) * searchReq.PageSize

	query := s.db.Model(&customer.Customer{})

	// Apply search query
	if searchReq.Query != "" {
		searchTerms := strings.Fields(searchReq.Query)
		for _, term := range searchTerms {
			query = query.Where("name ILIKE ? OR email ILIKE ? OR primary_phone ILIKE ? OR company ILIKE ?",
				"%"+term+"%", "%"+term+"%", "%"+term+"%", "%"+term+"%")
		}
	}

	// Apply filters
	for key, value := range searchReq.Filters {
		switch key {
		case "customer_type":
			query = query.Where("customer_type = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "district":
			if address, ok := searchReq.Filters["address"]; ok && strings.Contains(address, value) {
				query = query.Where("address->>'district' = ?", value)
			}
		case "satisfaction_min":
			query = query.Where("satisfaction_score >= ?", value)
		case "satisfaction_max":
			query = query.Where("satisfaction_score <= ?", value)
		case "lifetime_value_min":
			query = query.Where("lifetime_value >= ?", value)
		case "created_after":
			query = query.Where("created_at >= ?", value)
		case "created_before":
			query = query.Where("created_at <= ?", value)
		}
	}

	// Get total count
	var totalCount int64
	query.Count(&totalCount)

	// Apply sorting
	orderClause := searchReq.SortBy + " " + searchReq.SortOrder
	query = query.Order(orderClause)

	// Get customers
	var customers []*customer.Customer
	err := query.Offset(offset).Limit(searchReq.PageSize).Find(&customers).Error
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((totalCount + int64(searchReq.PageSize) - 1) / int64(searchReq.PageSize))

	response := &CustomerListResponse{
		Customers:   customers,
		TotalCount:  totalCount,
		Page:        searchReq.Page,
		PageSize:    searchReq.PageSize,
		TotalPages:  totalPages,
		HasNext:     searchReq.Page < totalPages,
		HasPrevious: searchReq.Page > 1,
	}

	c.JSON(200, response)
}

// 📊 Customer Analytics Handler
func (s *CustomerManagementServer) handleCustomerAnalytics(c *gin.Context) {
	timeRange := c.DefaultQuery("time_range", "30d")
	customerType := c.Query("customer_type")

	analytics := map[string]interface{}{
		"time_range": timeRange,
		"filters": map[string]string{
			"customer_type": customerType,
		},
		"metrics": map[string]interface{}{
			"total_customers":        s.getCustomerCount(customerType),
			"new_customers":          s.getNewCustomersCount(timeRange, customerType),
			"active_customers":       s.getActiveCustomersCount(customerType),
			"satisfaction_trend":     s.getSatisfactionTrend(timeRange, customerType),
			"lifetime_value_trend":   s.getLifetimeValueTrend(timeRange, customerType),
			"churn_analysis":         s.getChurnAnalysis(timeRange, customerType),
			"geographic_distribution": s.getGeographicDistribution(customerType),
		},
		"timestamp": time.Now(),
	}

	c.JSON(200, analytics)
}

// 📊 Get Dashboard Data
func (s *CustomerManagementServer) getDashboardData() (*CustomerDashboardData, error) {
	var totalCustomers, newThisMonth, activeCustomers, highValueCustomers int64

	// Total customers
	s.db.Model(&customer.Customer{}).Count(&totalCustomers)

	// New this month
	firstOfMonth := time.Now().AddDate(0, 0, -time.Now().Day()+1)
	s.db.Model(&customer.Customer{}).Where("created_at >= ?", firstOfMonth).Count(&newThisMonth)

	// Active customers
	s.db.Model(&customer.Customer{}).Where("status = ?", "active").Count(&activeCustomers)

	// High value customers (lifetime value > 10000 PLN)
	s.db.Model(&customer.Customer{}).Where("lifetime_value > ?", 10000).Count(&highValueCustomers)

	// Customers by type
	customersByType := make(map[string]int64)
	rows, _ := s.db.Model(&customer.Customer{}).
		Select("customer_type, COUNT(*)").
		Group("customer_type").Rows()
	defer rows.Close()

	for rows.Next() {
		var customerType string
		var count int64
		rows.Scan(&customerType, &count)
		customersByType[customerType] = count
	}

	// Customers by district (from address JSONB)
	customersByDistrict := make(map[string]int64)
	// This would require more complex JSONB queries in a real implementation
	customersByDistrict["Śródmieście"] = 45
	customersByDistrict["Mokotów"] = 38
	customersByDistrict["Żoliborz"] = 32
	customersByDistrict["Praga-Południe"] = 28
	customersByDistrict["Wola"] = 25

	// Recent customers
	var recentCustomers []*customer.Customer
	s.db.Order("created_at DESC").Limit(5).Find(&recentCustomers)

	// Top customers by lifetime value
	var topCustomers []*customer.Customer
	s.db.Order("lifetime_value DESC").Limit(5).Find(&topCustomers)

	// Customer metrics
	metrics, _ := s.getCustomerMetrics()

	return &CustomerDashboardData{
		TotalCustomers:      totalCustomers,
		NewThisMonth:        newThisMonth,
		ActiveCustomers:     activeCustomers,
		HighValueCustomers:  highValueCustomers,
		CustomersByType:     customersByType,
		CustomersByDistrict: customersByDistrict,
		RecentCustomers:     recentCustomers,
		TopCustomers:        topCustomers,
		CustomerMetrics:     metrics,
		Timestamp:           time.Now(),
	}, nil
}

// 📈 Get Customer Metrics
func (s *CustomerManagementServer) getCustomerMetrics() (*CustomerMetrics, error) {
	var avgSatisfaction, avgLifetimeValue float64
	var serviceRequestsCount int64

	// Average satisfaction
	s.db.Model(&customer.Customer{}).
		Select("AVG(satisfaction_score)").
		Scan(&avgSatisfaction)

	// Average lifetime value
	s.db.Model(&customer.Customer{}).
		Select("AVG(lifetime_value)").
		Scan(&avgLifetimeValue)

	// Service requests per month (placeholder)
	serviceRequestsCount = 156

	// Calculate churn rate (simplified)
	var totalCustomers, inactiveCustomers int64
	s.db.Model(&customer.Customer{}).Count(&totalCustomers)
	s.db.Model(&customer.Customer{}).Where("status = ?", "inactive").Count(&inactiveCustomers)
	
	churnRate := 0.0
	if totalCustomers > 0 {
		churnRate = (float64(inactiveCustomers) / float64(totalCustomers)) * 100
	}

	// Customer growth rate (simplified - last 30 days vs previous 30 days)
	now := time.Now()
	thirtyDaysAgo := now.AddDate(0, 0, -30)
	sixtyDaysAgo := now.AddDate(0, 0, -60)

	var currentPeriodCustomers, previousPeriodCustomers int64
	s.db.Model(&customer.Customer{}).Where("created_at >= ?", thirtyDaysAgo).Count(&currentPeriodCustomers)
	s.db.Model(&customer.Customer{}).Where("created_at >= ? AND created_at < ?", sixtyDaysAgo, thirtyDaysAgo).Count(&previousPeriodCustomers)

	growthRate := 0.0
	if previousPeriodCustomers > 0 {
		growthRate = ((float64(currentPeriodCustomers) - float64(previousPeriodCustomers)) / float64(previousPeriodCustomers)) * 100
	}

	return &CustomerMetrics{
		AverageSatisfaction:     avgSatisfaction,
		AverageLifetimeValue:    avgLifetimeValue,
		ChurnRate:               churnRate,
		CustomerGrowthRate:      growthRate,
		ServiceRequestsPerMonth: serviceRequestsCount,
	}, nil
}

// 📡 Broadcast Update to WebSocket Clients
func (s *CustomerManagementServer) broadcastUpdate(eventType string, data interface{}) {
	message := map[string]interface{}{
		"type":      eventType,
		"data":      data,
		"timestamp": time.Now(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshaling WebSocket message: %v", err)
		return
	}

	for clientID, conn := range s.wsConnections {
		err := conn.WriteMessage(1, messageBytes)
		if err != nil {
			log.Printf("Error sending WebSocket message to client %s: %v", clientID, err)
			conn.Close()
			delete(s.wsConnections, clientID)
		}
	}
}

// Helper functions for analytics (simplified implementations)

func (s *CustomerManagementServer) getCustomerCount(customerType string) int64 {
	var count int64
	query := s.db.Model(&customer.Customer{})
	if customerType != "" {
		query = query.Where("customer_type = ?", customerType)
	}
	query.Count(&count)
	return count
}

func (s *CustomerManagementServer) getNewCustomersCount(timeRange, customerType string) int64 {
	var count int64
	query := s.db.Model(&customer.Customer{})
	
	// Parse time range
	var since time.Time
	switch timeRange {
	case "7d":
		since = time.Now().AddDate(0, 0, -7)
	case "30d":
		since = time.Now().AddDate(0, 0, -30)
	case "90d":
		since = time.Now().AddDate(0, 0, -90)
	default:
		since = time.Now().AddDate(0, 0, -30)
	}
	
	query = query.Where("created_at >= ?", since)
	if customerType != "" {
		query = query.Where("customer_type = ?", customerType)
	}
	query.Count(&count)
	return count
}

func (s *CustomerManagementServer) getActiveCustomersCount(customerType string) int64 {
	var count int64
	query := s.db.Model(&customer.Customer{}).Where("status = ?", "active")
	if customerType != "" {
		query = query.Where("customer_type = ?", customerType)
	}
	query.Count(&count)
	return count
}

func (s *CustomerManagementServer) getSatisfactionTrend(timeRange, customerType string) []map[string]interface{} {
	// Simplified implementation - would need more complex time-series queries
	return []map[string]interface{}{
		{"date": "2024-01-01", "satisfaction": 4.2},
		{"date": "2024-01-15", "satisfaction": 4.3},
		{"date": "2024-02-01", "satisfaction": 4.1},
		{"date": "2024-02-15", "satisfaction": 4.4},
	}
}

func (s *CustomerManagementServer) getLifetimeValueTrend(timeRange, customerType string) []map[string]interface{} {
	// Simplified implementation
	return []map[string]interface{}{
		{"date": "2024-01-01", "avg_ltv": 8500},
		{"date": "2024-01-15", "avg_ltv": 8750},
		{"date": "2024-02-01", "avg_ltv": 9200},
		{"date": "2024-02-15", "avg_ltv": 9500},
	}
}

func (s *CustomerManagementServer) getChurnAnalysis(timeRange, customerType string) map[string]interface{} {
	return map[string]interface{}{
		"churn_rate":     2.3,
		"at_risk_count":  12,
		"churned_count":  8,
		"retention_rate": 97.7,
	}
}

func (s *CustomerManagementServer) getGeographicDistribution(customerType string) map[string]int64 {
	return map[string]int64{
		"Śródmieście":     45,
		"Mokotów":         38,
		"Żoliborz":        32,
		"Praga-Południe":  28,
		"Wola":            25,
		"Ursynów":         22,
		"Bemowo":          18,
		"Targówek":        15,
	}
}
