package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/customer"
)

// 🎛️ Enhanced Customer Management Interface
// Comprehensive customer management with Polish HVAC-specific features

type CustomerManagementServer struct {
	db                *gorm.DB
	customerService   *customer.CustomerIntelligenceService
	wsUpgrader        websocket.Upgrader
	wsConnections     map[string]*websocket.Conn
	router            *gin.Engine
}

// 📊 Customer Dashboard Data
type CustomerDashboardData struct {
	TotalCustomers     int64                    `json:"total_customers"`
	NewThisMonth       int64                    `json:"new_this_month"`
	ActiveCustomers    int64                    `json:"active_customers"`
	HighValueCustomers int64                    `json:"high_value_customers"`
	CustomersByType    map[string]int64         `json:"customers_by_type"`
	CustomersByDistrict map[string]int64        `json:"customers_by_district"`
	RecentCustomers    []*customer.Customer     `json:"recent_customers"`
	TopCustomers       []*customer.Customer     `json:"top_customers"`
	CustomerMetrics    *CustomerMetrics         `json:"customer_metrics"`
	Timestamp          time.Time                `json:"timestamp"`
}

// 📈 Customer Metrics
type CustomerMetrics struct {
	AverageSatisfaction    float64 `json:"average_satisfaction"`
	AverageLifetimeValue   float64 `json:"average_lifetime_value"`
	ChurnRate              float64 `json:"churn_rate"`
	CustomerGrowthRate     float64 `json:"customer_growth_rate"`
	ServiceRequestsPerMonth int64   `json:"service_requests_per_month"`
}

// 🔍 Customer Search Request
type CustomerSearchRequest struct {
	Query       string            `json:"query"`
	Filters     map[string]string `json:"filters"`
	Page        int               `json:"page"`
	PageSize    int               `json:"page_size"`
	SortBy      string            `json:"sort_by"`
	SortOrder   string            `json:"sort_order"`
}

// 📋 Customer List Response
type CustomerListResponse struct {
	Customers    []*customer.Customer `json:"customers"`
	TotalCount   int64                `json:"total_count"`
	Page         int                  `json:"page"`
	PageSize     int                  `json:"page_size"`
	TotalPages   int                  `json:"total_pages"`
	HasNext      bool                 `json:"has_next"`
	HasPrevious  bool                 `json:"has_previous"`
}

func main() {
	// Initialize database connection
	dsn := "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable TimeZone=Europe/Warsaw"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize customer service
	customerService := customer.NewCustomerIntelligenceService(db, nil, &customer.CustomerConfig{
		AutoCreateCustomers:  true,
		MatchingThreshold:   0.8,
		EnableAIEnrichment:  true,
		DefaultSatisfaction: 3.0,
		AnalyticsUpdateFreq: "daily",
	}, nil)

	// Initialize server
	server := &CustomerManagementServer{
		db:              db,
		customerService: customerService,
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for development
			},
		},
		wsConnections: make(map[string]*websocket.Conn),
	}

	// Setup routes
	server.setupRoutes()

	// Start server
	fmt.Println("🎛️ Enhanced Customer Management Interface starting on :8091")
	fmt.Println("📊 Dashboard: http://localhost:8091/dashboard")
	fmt.Println("🔗 API: http://localhost:8091/api/v1/customers")
	
	log.Fatal(http.ListenAndServe(":8091", server.router))
}

func (s *CustomerManagementServer) setupRoutes() {
	s.router = gin.Default()

	// Enable CORS
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Static files for dashboard
	s.router.Static("/static", "./static")
	
	// Dashboard routes
	s.router.GET("/", s.handleDashboard)
	s.router.GET("/dashboard", s.handleDashboard)
	s.router.GET("/ws", s.handleWebSocket)

	// API routes
	api := s.router.Group("/api/v1")
	{
		// Customer management
		customers := api.Group("/customers")
		{
			customers.GET("", s.handleGetCustomers)
			customers.POST("", s.handleCreateCustomer)
			customers.GET("/:id", s.handleGetCustomer)
			customers.PUT("/:id", s.handleUpdateCustomer)
			customers.DELETE("/:id", s.handleDeleteCustomer)
			customers.GET("/:id/profile", s.handleGetCustomerProfile)
			customers.GET("/:id/equipment", s.handleGetCustomerEquipment)
			customers.GET("/:id/interactions", s.handleGetCustomerInteractions)
			customers.POST("/:id/interactions", s.handleCreateCustomerInteraction)
		}

		// Dashboard data
		dashboard := api.Group("/dashboard")
		{
			dashboard.GET("/overview", s.handleDashboardOverview)
			dashboard.GET("/metrics", s.handleDashboardMetrics)
			dashboard.GET("/recent-activity", s.handleRecentActivity)
		}

		// Search and analytics
		api.POST("/search/customers", s.handleSearchCustomers)
		api.GET("/analytics/customers", s.handleCustomerAnalytics)

		// 🎯 Lead Scoring System (LiveSpace inspired)
		leadScoring := api.Group("/lead-scoring")
		{
			leadScoring.GET("/criteria", s.handleGetLeadScoringCriteria)
			leadScoring.POST("/calculate/:id", s.handleCalculateLeadScore)
			leadScoring.POST("/bulk-calculate", s.handleBulkLeadScoring)
		}

		// 🚀 Pipeline Management (LiveSpace inspired)
		pipeline := api.Group("/pipeline")
		{
			pipeline.GET("/stages", s.handleGetPipelineStages)
			pipeline.GET("/opportunities/stage/:stage_id", s.handleGetOpportunitiesByStage)
			pipeline.GET("/analytics", s.handleGetPipelineAnalytics)
		}

		// 💡 Knowledge Sharing (LiveSpace "Podpowiedzi" inspired)
		knowledge := api.Group("/knowledge")
		{
			knowledge.GET("/search", s.handleSearchKnowledge)
			knowledge.GET("/entry/:id", s.handleGetKnowledgeEntry)
			knowledge.GET("/suggestions", s.handleGetSmartSuggestions)
			knowledge.GET("/analytics", s.handleGetKnowledgeAnalytics)
		}
	}
}

// 📊 Dashboard Handler
func (s *CustomerManagementServer) handleDashboard(c *gin.Context) {
	dashboardHTML := `
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ Zarządzanie Klientami HVAC</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #ecf0f1;
            min-height: 100vh;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; color: #e94560; }
        .header p { font-size: 1.1rem; color: #bdc3c7; }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card h3 { color: #f39c12; margin-bottom: 16px; font-size: 1.2rem; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 12px; }
        .metric-value { font-weight: bold; color: #2ecc71; }
        .customer-list { margin-top: 40px; }
        .customer-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 8px;
            border-left: 4px solid #e94560;
        }
        .customer-name { font-weight: bold; color: #ecf0f1; }
        .customer-details { color: #bdc3c7; font-size: 0.9rem; margin-top: 4px; }
        .loading { text-align: center; padding: 40px; color: #bdc3c7; }
        .refresh-btn {
            background: #e94560;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin: 20px auto;
            display: block;
        }
        .refresh-btn:hover { background: #c73650; }
        .nav-btn {
            background: rgba(255, 255, 255, 0.1);
            color: #ecf0f1;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        .nav-btn:hover { background: rgba(255, 255, 255, 0.2); }
        .nav-btn.active { background: #e94560; border-color: #e94560; }
        .section { display: none; }
        .section.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎛️ Zarządzanie Klientami HVAC</h1>
            <p>Kompleksowy system zarządzania klientami dla branży HVAC z funkcjami LiveSpace CRM</p>
            <div style="margin-top: 20px;">
                <button onclick="showSection('dashboard')" class="nav-btn active" id="nav-dashboard">📊 Dashboard</button>
                <button onclick="showSection('lead-scoring')" class="nav-btn" id="nav-lead-scoring">🎯 Lead Scoring</button>
                <button onclick="showSection('pipeline')" class="nav-btn" id="nav-pipeline">🚀 Pipeline</button>
                <button onclick="showSection('knowledge')" class="nav-btn" id="nav-knowledge">💡 Wiedza</button>
            </div>
        </div>

        <div id="dashboard-content" class="loading">
            <p>Ładowanie danych...</p>
        </div>

        <button class="refresh-btn" onclick="loadDashboard()">🔄 Odśwież dane</button>
    </div>

    <script>
        let ws;

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8091/ws');
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'dashboard_update') {
                    updateDashboard(data.data);
                }
            };
        }

        async function loadDashboard() {
            try {
                const response = await fetch('/api/v1/dashboard/overview');
                const data = await response.json();
                updateDashboard(data);
            } catch (error) {
                console.error('Error loading dashboard:', error);
                document.getElementById('dashboard-content').innerHTML =
                    '<p style="color: #e74c3c;">Błąd ładowania danych</p>';
            }
        }

        function updateDashboard(data) {
            const content = document.getElementById('dashboard-content');
            content.innerHTML = generateDashboardHTML(data);
        }

        function generateDashboardHTML(data) {
            return ` + "`" + `
                <div class="dashboard-grid">
                    <div class="card">
                        <h3>📊 Statystyki Klientów</h3>
                        <div class="metric">
                            <span>Łączna liczba klientów:</span>
                            <span class="metric-value">${data.total_customers || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Nowi w tym miesiącu:</span>
                            <span class="metric-value">${data.new_this_month || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Aktywni klienci:</span>
                            <span class="metric-value">${data.active_customers || 0}</span>
                        </div>
                        <div class="metric">
                            <span>Klienci VIP:</span>
                            <span class="metric-value">${data.high_value_customers || 0}</span>
                        </div>
                    </div>

                    <div class="card">
                        <h3>🏢 Typy Klientów</h3>
                        ${Object.entries(data.customers_by_type || {}).map(([type, count]) => ` + "`" + `
                            <div class="metric">
                                <span>${translateCustomerType(type)}:</span>
                                <span class="metric-value">${count}</span>
                            </div>
                        ` + "`" + `).join('')}
                    </div>

                    <div class="card">
                        <h3>🗺️ Klienci wg Dzielnic</h3>
                        ${Object.entries(data.customers_by_district || {}).map(([district, count]) => ` + "`" + `
                            <div class="metric">
                                <span>${district}:</span>
                                <span class="metric-value">${count}</span>
                            </div>
                        ` + "`" + `).join('')}
                    </div>

                    <div class="card">
                        <h3>📈 Metryki Biznesowe</h3>
                        <div class="metric">
                            <span>Średnia satysfakcja:</span>
                            <span class="metric-value">${(data.customer_metrics?.average_satisfaction || 0).toFixed(1)}/5.0</span>
                        </div>
                        <div class="metric">
                            <span>Średnia wartość klienta:</span>
                            <span class="metric-value">${(data.customer_metrics?.average_lifetime_value || 0).toFixed(0)} zł</span>
                        </div>
                        <div class="metric">
                            <span>Wskaźnik odejść:</span>
                            <span class="metric-value">${(data.customer_metrics?.churn_rate || 0).toFixed(1)}%</span>
                        </div>
                    </div>
                </div>

                <div class="customer-list">
                    <h3 style="color: #f39c12; margin-bottom: 20px;">👥 Najnowsi Klienci</h3>
                    ${(data.recent_customers || []).map(customer => ` + "`" + `
                        <div class="customer-item">
                            <div class="customer-name">${customer.name || 'Brak nazwy'}</div>
                            <div class="customer-details">
                                📧 ${customer.email || 'Brak email'} |
                                📞 ${customer.primary_phone || 'Brak telefonu'} |
                                🏢 ${translateCustomerType(customer.customer_type || 'unknown')}
                            </div>
                        </div>
                    ` + "`" + `).join('')}
                </div>
            ` + "`" + `;
        }

        function translateCustomerType(type) {
            const translations = {
                'residential': 'Mieszkaniowy',
                'commercial': 'Komercyjny',
                'industrial': 'Przemysłowy',
                'unknown': 'Nieznany'
            };
            return translations[type] || type;
        }

        // Navigation functions
        function showSection(sectionName) {
            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => section.classList.remove('active'));

            // Remove active class from all nav buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach(btn => btn.classList.remove('active'));

            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Activate corresponding nav button
            const navButton = document.getElementById('nav-' + sectionName);
            if (navButton) {
                navButton.classList.add('active');
            }

            // Load section-specific data
            switch(sectionName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'lead-scoring':
                    loadLeadScoring();
                    break;
                case 'pipeline':
                    loadPipeline();
                    break;
                case 'knowledge':
                    loadKnowledge();
                    break;
            }
        }

        async function loadLeadScoring() {
            try {
                const response = await fetch('/api/v1/lead-scoring/criteria');
                const data = await response.json();
                updateLeadScoringSection(data);
            } catch (error) {
                console.error('Error loading lead scoring:', error);
            }
        }

        async function loadPipeline() {
            try {
                const response = await fetch('/api/v1/pipeline/stages');
                const data = await response.json();
                updatePipelineSection(data);
            } catch (error) {
                console.error('Error loading pipeline:', error);
            }
        }

        async function loadKnowledge() {
            try {
                const response = await fetch('/api/v1/knowledge/search');
                const data = await response.json();
                updateKnowledgeSection(data);
            } catch (error) {
                console.error('Error loading knowledge:', error);
            }
        }

        function updateLeadScoringSection(data) {
            // Implementation for lead scoring section
            console.log('Lead scoring data:', data);
        }

        function updatePipelineSection(data) {
            // Implementation for pipeline section
            console.log('Pipeline data:', data);
        }

        function updateKnowledgeSection(data) {
            // Implementation for knowledge section
            console.log('Knowledge data:', data);
        }

        // Initialize
        loadDashboard();
        connectWebSocket();
    </script>
</body>
</html>`

	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, dashboardHTML)
}
