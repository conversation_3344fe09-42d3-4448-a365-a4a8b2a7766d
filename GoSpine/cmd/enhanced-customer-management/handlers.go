package main

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"gobackend-hvac-kratos/internal/customer"
)

// 🌐 WebSocket Handler
func (s *CustomerManagementServer) handleWebSocket(c *gin.Context) {
	conn, err := s.wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to upgrade to WebSocket"})
		return
	}
	defer conn.Close()

	clientID := uuid.New().String()
	s.wsConnections[clientID] = conn

	// Send initial data
	dashboardData, _ := s.getDashboardData()
	conn.WriteJSON(map[string]interface{}{
		"type": "dashboard_update",
		"data": dashboardData,
	})

	// Keep connection alive
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			delete(s.wsConnections, clientID)
			break
		}
	}
}

// 📊 Dashboard Overview Handler
func (s *CustomerManagementServer) handleDashboardOverview(c *gin.Context) {
	data, err := s.getDashboardData()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, data)
}

// 📈 Dashboard Metrics Handler
func (s *CustomerManagementServer) handleDashboardMetrics(c *gin.Context) {
	metrics, err := s.getCustomerMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// 🔄 Recent Activity Handler
func (s *CustomerManagementServer) handleRecentActivity(c *gin.Context) {
	var recentCustomers []*customer.Customer
	err := s.db.Order("created_at DESC").Limit(10).Find(&recentCustomers).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"recent_customers": recentCustomers,
		"timestamp":        time.Now(),
	})
}

// 👥 Get Customers Handler
func (s *CustomerManagementServer) handleGetCustomers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	search := c.Query("search")
	customerType := c.Query("type")
	status := c.Query("status")

	offset := (page - 1) * pageSize

	query := s.db.Model(&customer.Customer{})

	// Apply filters
	if search != "" {
		query = query.Where("name ILIKE ? OR email ILIKE ? OR primary_phone ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	if customerType != "" {
		query = query.Where("customer_type = ?", customerType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	var totalCount int64
	query.Count(&totalCount)

	// Get customers
	var customers []*customer.Customer
	err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&customers).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := int((totalCount + int64(pageSize) - 1) / int64(pageSize))

	response := &CustomerListResponse{
		Customers:   customers,
		TotalCount:  totalCount,
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}

	c.JSON(http.StatusOK, response)
}

// 👤 Get Customer Handler
func (s *CustomerManagementServer) handleGetCustomer(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	var customer customer.Customer
	err = s.db.Preload("Phones").Preload("Emails").First(&customer, customerID).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Customer not found"})
		return
	}

	c.JSON(http.StatusOK, customer)
}

// ➕ Create Customer Handler
func (s *CustomerManagementServer) handleCreateCustomer(c *gin.Context) {
	var newCustomer customer.Customer
	if err := c.ShouldBindJSON(&newCustomer); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	newCustomer.ID = uuid.New()
	if newCustomer.Status == "" {
		newCustomer.Status = "active"
	}
	if newCustomer.CustomerType == "" {
		newCustomer.CustomerType = "residential"
	}
	if newCustomer.SatisfactionScore == 0 {
		newCustomer.SatisfactionScore = 3.0
	}

	err := s.db.Create(&newCustomer).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Broadcast update to WebSocket clients
	s.broadcastUpdate("customer_created", newCustomer)

	c.JSON(http.StatusCreated, newCustomer)
}

// ✏️ Update Customer Handler
func (s *CustomerManagementServer) handleUpdateCustomer(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	var updateData customer.Customer
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	err = s.db.Model(&customer.Customer{}).Where("id = ?", customerID).Updates(updateData).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get updated customer
	var updatedCustomer customer.Customer
	s.db.First(&updatedCustomer, customerID)

	// Broadcast update to WebSocket clients
	s.broadcastUpdate("customer_updated", updatedCustomer)

	c.JSON(http.StatusOK, updatedCustomer)
}

// 🗑️ Delete Customer Handler
func (s *CustomerManagementServer) handleDeleteCustomer(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	err = s.db.Delete(&customer.Customer{}, customerID).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Broadcast update to WebSocket clients
	s.broadcastUpdate("customer_deleted", gin.H{"id": customerID})

	c.JSON(http.StatusOK, gin.H{"message": "Customer deleted successfully"})
}

// 📋 Get Customer Profile Handler
func (s *CustomerManagementServer) handleGetCustomerProfile(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	profile, err := s.customerService.BuildCustomerProfile(c.Request.Context(), customerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, profile)
}

// 🔧 Get Customer Equipment Handler
func (s *CustomerManagementServer) handleGetCustomerEquipment(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	// This would typically query an equipment table
	// For now, return placeholder data
	equipment := []map[string]interface{}{
		{
			"id":               1,
			"type":             "air_conditioner",
			"brand":            "Daikin",
			"model":            "FTXS35K",
			"serial_number":    "*********",
			"installation_date": "2024-03-15",
			"status":           "active",
			"health_score":     0.95,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"customer_id": customerID,
		"equipment":   equipment,
	})
}

// 💬 Get Customer Interactions Handler
func (s *CustomerManagementServer) handleGetCustomerInteractions(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	var interactions []*customer.CustomerInteraction
	err = s.db.Where("customer_id = ?", customerID).
		Order("timestamp DESC").
		Limit(50).
		Find(&interactions).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"customer_id":   customerID,
		"interactions":  interactions,
		"total_count":   len(interactions),
	})
}

// ➕ Create Customer Interaction Handler
func (s *CustomerManagementServer) handleCreateCustomerInteraction(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
		return
	}

	var interaction customer.CustomerInteraction
	if err := c.ShouldBindJSON(&interaction); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	interaction.ID = uuid.New()
	interaction.CustomerID = customerID
	interaction.Timestamp = time.Now()

	err = s.db.Create(&interaction).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, interaction)
}
