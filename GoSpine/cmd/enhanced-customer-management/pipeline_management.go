package main

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// 🚀 Pipeline Management System - Inspired by LiveSpace CRM
// 7-etapowy proces sprzedaży dla branży HVAC

// 📋 Sales Pipeline Stage
type SalesPipelineStage struct {
	ID                uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name              string    `json:"name" gorm:"not null"`
	NamePL            string    `json:"name_pl" gorm:"not null"` // Polish name
	Description       string    `json:"description"`
	Order             int       `json:"order" gorm:"not null"`
	Color             string    `json:"color" gorm:"default:'#3498db'"`
	Icon              string    `json:"icon"`
	IsActive          bool      `json:"is_active" gorm:"default:true"`
	RequiredActions   []string  `json:"required_actions" gorm:"type:jsonb"`
	ExpectedDuration  int       `json:"expected_duration"` // Days
	ConversionRate    float64   `json:"conversion_rate"`   // Percentage
	AutoAdvanceRules  []string  `json:"auto_advance_rules" gorm:"type:jsonb"`
	HVACSpecific      bool      `json:"hvac_specific" gorm:"default:true"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// 🎯 Sales Opportunity
type SalesOpportunity struct {
	ID                  uuid.UUID          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CustomerID          uuid.UUID          `json:"customer_id" gorm:"type:uuid;not null"`
	Title               string             `json:"title" gorm:"not null"`
	Description         string             `json:"description"`
	CurrentStageID      uuid.UUID          `json:"current_stage_id" gorm:"type:uuid"`
	CurrentStage        *SalesPipelineStage `json:"current_stage,omitempty" gorm:"foreignKey:CurrentStageID"`
	Value               float64            `json:"value"` // PLN
	Probability         float64            `json:"probability"` // 0-100%
	ExpectedCloseDate   *time.Time         `json:"expected_close_date"`
	ActualCloseDate     *time.Time         `json:"actual_close_date"`
	Source              string             `json:"source"` // Website, Referral, Cold Call, etc.
	Priority            string             `json:"priority"` // Wysoki, Średni, Niski
	AssignedTo          string             `json:"assigned_to"`
	HVACServiceType     string             `json:"hvac_service_type"` // Installation, Maintenance, Repair
	EquipmentType       string             `json:"equipment_type"` // AC, Heating, Ventilation
	EquipmentBrand      string             `json:"equipment_brand"` // Daikin, LG, Mitsubishi
	WarsawDistrict      string             `json:"warsaw_district"`
	IsCommercial        bool               `json:"is_commercial"`
	ContractType        string             `json:"contract_type"` // One-time, Service Contract, Warranty
	TechnicalComplexity string             `json:"technical_complexity"` // Simple, Medium, Complex
	SeasonalFactor      bool               `json:"seasonal_factor"`
	CompetitorInfo      string             `json:"competitor_info"`
	Tags                []string           `json:"tags" gorm:"type:jsonb"`
	CustomFields        map[string]interface{} `json:"custom_fields" gorm:"type:jsonb"`
	Status              string             `json:"status"` // Open, Won, Lost, Postponed
	LostReason          string             `json:"lost_reason,omitempty"`
	CreatedAt           time.Time          `json:"created_at"`
	UpdatedAt           time.Time          `json:"updated_at"`
}

// 📊 Stage History
type StageHistory struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OpportunityID  uuid.UUID `json:"opportunity_id" gorm:"type:uuid;not null"`
	FromStageID    *uuid.UUID `json:"from_stage_id" gorm:"type:uuid"`
	ToStageID      uuid.UUID `json:"to_stage_id" gorm:"type:uuid;not null"`
	ChangedBy      string    `json:"changed_by"`
	ChangeReason   string    `json:"change_reason"`
	TimeInStage    int       `json:"time_in_stage"` // Hours
	Notes          string    `json:"notes"`
	CreatedAt      time.Time `json:"created_at"`
}

// 🎯 Pipeline Management Service
type PipelineManagementService struct {
	stages []SalesPipelineStage
}

// Initialize HVAC-specific 7-stage sales pipeline
func NewPipelineManagementService() *PipelineManagementService {
	service := &PipelineManagementService{}
	service.initializeHVACPipeline()
	return service
}

func (pms *PipelineManagementService) initializeHVACPipeline() {
	pms.stages = []SalesPipelineStage{
		{
			ID:               uuid.New(),
			Name:             "Lead Generation",
			NamePL:           "Pozyskanie Leada",
			Description:      "Pierwszy kontakt z potencjalnym klientem HVAC",
			Order:            1,
			Color:            "#e74c3c",
			Icon:             "🎯",
			ExpectedDuration: 1,
			ConversionRate:   85.0,
			RequiredActions:  []string{"Weryfikacja danych kontaktowych", "Wstępna kwalifikacja potrzeb", "Ustalenie terminu rozmowy"},
			AutoAdvanceRules: []string{"Kontakt nawiązany", "Dane zweryfikowane"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Qualification",
			NamePL:           "Kwalifikacja",
			Description:      "Szczegółowa analiza potrzeb klienta i możliwości technicznych",
			Order:            2,
			Color:            "#f39c12",
			Icon:             "🔍",
			ExpectedDuration: 3,
			ConversionRate:   70.0,
			RequiredActions:  []string{"Wizyta techniczna", "Analiza istniejących instalacji", "Określenie budżetu", "Identyfikacja decydentów"},
			AutoAdvanceRules: []string{"Wizyta techniczna zakończona", "Budżet potwierdzony"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Technical Assessment",
			NamePL:           "Ocena Techniczna",
			Description:      "Szczegółowa analiza techniczna i projektowanie rozwiązania HVAC",
			Order:            3,
			Color:            "#3498db",
			Icon:             "🔧",
			ExpectedDuration: 5,
			ConversionRate:   60.0,
			RequiredActions:  []string{"Pomiary techniczne", "Projekt instalacji", "Dobór urządzeń", "Kalkulacja kosztów"},
			AutoAdvanceRules: []string{"Projekt zatwierdzony", "Specyfikacja techniczna gotowa"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Proposal",
			NamePL:           "Oferta",
			Description:      "Przygotowanie i prezentacja szczegółowej oferty handlowej",
			Order:            4,
			Color:            "#9b59b6",
			Icon:             "📋",
			ExpectedDuration: 7,
			ConversionRate:   45.0,
			RequiredActions:  []string{"Przygotowanie oferty", "Prezentacja rozwiązania", "Negocjacje cenowe", "Ustalenie warunków płatności"},
			AutoAdvanceRules: []string{"Oferta przedstawiona", "Feedback otrzymany"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Negotiation",
			NamePL:           "Negocjacje",
			Description:      "Finalne negocjacje warunków kontraktu i harmonogramu",
			Order:            5,
			Color:            "#e67e22",
			Icon:             "🤝",
			ExpectedDuration: 10,
			ConversionRate:   35.0,
			RequiredActions:  []string{"Negocjacje cenowe", "Ustalenie harmonogramu", "Warunki gwarancji", "Finalizacja kontraktu"},
			AutoAdvanceRules: []string{"Warunki uzgodnione", "Kontrakt przygotowany"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Contract Signing",
			NamePL:           "Podpisanie Umowy",
			Description:      "Formalne podpisanie umowy i rozpoczęcie realizacji",
			Order:            6,
			Color:            "#27ae60",
			Icon:             "✍️",
			ExpectedDuration: 3,
			ConversionRate:   90.0,
			RequiredActions:  []string{"Podpisanie umowy", "Wpłata zaliczki", "Zamówienie materiałów", "Planowanie realizacji"},
			AutoAdvanceRules: []string{"Umowa podpisana", "Zaliczka wpłacona"},
			HVACSpecific:     true,
		},
		{
			ID:               uuid.New(),
			Name:             "Implementation",
			NamePL:           "Realizacja",
			Description:      "Wykonanie instalacji HVAC i odbiór techniczny",
			Order:            7,
			Color:            "#2ecc71",
			Icon:             "🏗️",
			ExpectedDuration: 14,
			ConversionRate:   95.0,
			RequiredActions:  []string{"Dostawa materiałów", "Montaż instalacji", "Testy i uruchomienie", "Odbiór techniczny", "Szkolenie użytkowników"},
			AutoAdvanceRules: []string{"Instalacja zakończona", "Odbiór podpisany"},
			HVACSpecific:     true,
		},
	}
}

// 📊 Pipeline Analytics
type PipelineAnalytics struct {
	TotalOpportunities    int                    `json:"total_opportunities"`
	TotalValue            float64                `json:"total_value"`
	AverageValue          float64                `json:"average_value"`
	AverageCycleTime      int                    `json:"average_cycle_time"` // Days
	ConversionRate        float64                `json:"conversion_rate"`
	StageDistribution     map[string]int         `json:"stage_distribution"`
	StageConversionRates  map[string]float64     `json:"stage_conversion_rates"`
	MonthlyTrends         []MonthlyTrend         `json:"monthly_trends"`
	HVACServiceBreakdown  map[string]int         `json:"hvac_service_breakdown"`
	WarsawDistrictStats   map[string]int         `json:"warsaw_district_stats"`
	EquipmentBrandStats   map[string]int         `json:"equipment_brand_stats"`
	SeasonalAnalysis      SeasonalAnalysis       `json:"seasonal_analysis"`
	TopPerformers         []PerformerStats       `json:"top_performers"`
}

// 📈 Monthly Trend
type MonthlyTrend struct {
	Month              string  `json:"month"`
	OpportunitiesCount int     `json:"opportunities_count"`
	TotalValue         float64 `json:"total_value"`
	ConversionRate     float64 `json:"conversion_rate"`
	AverageCycleTime   int     `json:"average_cycle_time"`
}

// 🌡️ Seasonal Analysis
type SeasonalAnalysis struct {
	SpringStats  SeasonStats `json:"spring_stats"`
	SummerStats  SeasonStats `json:"summer_stats"`
	AutumnStats  SeasonStats `json:"autumn_stats"`
	WinterStats  SeasonStats `json:"winter_stats"`
}

// 📊 Season Statistics
type SeasonStats struct {
	OpportunitiesCount int     `json:"opportunities_count"`
	TotalValue         float64 `json:"total_value"`
	ConversionRate     float64 `json:"conversion_rate"`
	DominantService    string  `json:"dominant_service"`
}

// 🏆 Performer Statistics
type PerformerStats struct {
	AssignedTo         string  `json:"assigned_to"`
	OpportunitiesCount int     `json:"opportunities_count"`
	TotalValue         float64 `json:"total_value"`
	ConversionRate     float64 `json:"conversion_rate"`
	AverageCycleTime   int     `json:"average_cycle_time"`
}

// 🎯 Get Pipeline Stages
func (pms *PipelineManagementService) GetStages() []SalesPipelineStage {
	return pms.stages
}

// 📊 Get Stage by ID
func (pms *PipelineManagementService) GetStageByID(stageID uuid.UUID) *SalesPipelineStage {
	for _, stage := range pms.stages {
		if stage.ID == stageID {
			return &stage
		}
	}
	return nil
}

// ⬆️ Advance Opportunity to Next Stage
func (pms *PipelineManagementService) AdvanceOpportunity(opportunity *SalesOpportunity, reason string, changedBy string) error {
	currentStage := pms.GetStageByID(opportunity.CurrentStageID)
	if currentStage == nil {
		return gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic}
	}

	// Find next stage
	var nextStage *SalesPipelineStage
	for _, stage := range pms.stages {
		if stage.Order == currentStage.Order+1 {
			nextStage = &stage
			break
		}
	}

	if nextStage == nil {
		// Already at final stage
		opportunity.Status = "Won"
		opportunity.ActualCloseDate = &[]time.Time{time.Now()}[0]
		return nil
	}

	// Update opportunity
	opportunity.CurrentStageID = nextStage.ID
	opportunity.UpdatedAt = time.Now()

	return nil
}

// 📊 Calculate Pipeline Analytics
func (pms *PipelineManagementService) CalculatePipelineAnalytics(opportunities []SalesOpportunity) *PipelineAnalytics {
	analytics := &PipelineAnalytics{
		StageDistribution:    make(map[string]int),
		StageConversionRates: make(map[string]float64),
		HVACServiceBreakdown: make(map[string]int),
		WarsawDistrictStats:  make(map[string]int),
		EquipmentBrandStats:  make(map[string]int),
	}

	totalValue := 0.0
	totalOpportunities := len(opportunities)
	wonOpportunities := 0

	// Analyze opportunities
	for _, opp := range opportunities {
		analytics.TotalOpportunities++
		totalValue += opp.Value

		// Stage distribution
		if stage := pms.GetStageByID(opp.CurrentStageID); stage != nil {
			analytics.StageDistribution[stage.NamePL]++
		}

		// Service type breakdown
		if opp.HVACServiceType != "" {
			analytics.HVACServiceBreakdown[opp.HVACServiceType]++
		}

		// Warsaw district stats
		if opp.WarsawDistrict != "" {
			analytics.WarsawDistrictStats[opp.WarsawDistrict]++
		}

		// Equipment brand stats
		if opp.EquipmentBrand != "" {
			analytics.EquipmentBrandStats[opp.EquipmentBrand]++
		}

		// Count won opportunities
		if opp.Status == "Won" {
			wonOpportunities++
		}
	}

	analytics.TotalValue = totalValue
	if totalOpportunities > 0 {
		analytics.AverageValue = totalValue / float64(totalOpportunities)
		analytics.ConversionRate = (float64(wonOpportunities) / float64(totalOpportunities)) * 100
	}

	return analytics
}

// 🎯 API Handlers for Pipeline Management

// Get all pipeline stages
func (s *CustomerManagementServer) handleGetPipelineStages(c *gin.Context) {
	pipelineService := NewPipelineManagementService()
	stages := pipelineService.GetStages()

	c.JSON(200, gin.H{
		"stages": stages,
		"total_stages": len(stages),
		"pipeline_type": "HVAC 7-Stage Sales Process",
	})
}

// Get opportunities by stage
func (s *CustomerManagementServer) handleGetOpportunitiesByStage(c *gin.Context) {
	stageID := c.Param("stage_id")
	
	// This would query the database for opportunities
	// For now, return mock data
	opportunities := []SalesOpportunity{
		{
			ID:                uuid.New(),
			Title:             "Klimatyzacja biurowca - Mokotów",
			Value:             85000.0,
			Probability:       75.0,
			HVACServiceType:   "Installation",
			EquipmentType:     "AC",
			EquipmentBrand:    "Daikin",
			WarsawDistrict:    "Mokotów",
			IsCommercial:      true,
			Priority:          "Wysoki",
			AssignedTo:        "Jan Kowalski",
			CreatedAt:         time.Now().AddDate(0, 0, -5),
		},
	}

	c.JSON(200, gin.H{
		"stage_id": stageID,
		"opportunities": opportunities,
		"total_count": len(opportunities),
	})
}

// Get pipeline analytics
func (s *CustomerManagementServer) handleGetPipelineAnalytics(c *gin.Context) {
	pipelineService := NewPipelineManagementService()
	
	// Mock opportunities data
	opportunities := []SalesOpportunity{
		{
			ID:                uuid.New(),
			Value:             85000.0,
			Status:            "Open",
			HVACServiceType:   "Installation",
			WarsawDistrict:    "Mokotów",
			EquipmentBrand:    "Daikin",
			CreatedAt:         time.Now().AddDate(0, 0, -10),
		},
		{
			ID:                uuid.New(),
			Value:             45000.0,
			Status:            "Won",
			HVACServiceType:   "Maintenance",
			WarsawDistrict:    "Śródmieście",
			EquipmentBrand:    "LG",
			CreatedAt:         time.Now().AddDate(0, 0, -20),
		},
	}

	analytics := pipelineService.CalculatePipelineAnalytics(opportunities)

	c.JSON(200, gin.H{
		"analytics": analytics,
		"generated_at": time.Now(),
		"pipeline_type": "HVAC Sales Pipeline",
	})
}
