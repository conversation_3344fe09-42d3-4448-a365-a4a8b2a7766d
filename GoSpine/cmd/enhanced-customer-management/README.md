# 🎛️ Enhanced Customer Management Interface

## 📋 Overview

Comprehensive customer management interface for GoSpine HVAC CRM system with Polish language support and HVAC-specific features.

## ✨ Features

### 📊 Dashboard
- Real-time customer statistics
- Customer segmentation by type (Mieszkaniowy, Komercyjny, Przemysłowy)
- Geographic distribution by Warsaw districts
- Business metrics (satisfaction, lifetime value, churn rate)
- Recent customer activity

### 👥 Customer Management
- Complete customer CRUD operations
- Advanced search and filtering
- Customer profile with 360-degree view
- Equipment tracking per customer
- Interaction history timeline
- Real-time updates via WebSocket

### 🔍 Advanced Features
- AI-powered customer intelligence
- Customer health scoring
- Churn prediction
- Upsell opportunity identification
- Polish HVAC terminology
- Warsaw district optimization

## 🚀 Quick Start

### Prerequisites
- Go 1.23+
- PostgreSQL database
- GoSpine project dependencies

### Build and Run
```bash
# Build the application
go build -o bin/enhanced-customer-management ./cmd/enhanced-customer-management

# Run the application
./bin/enhanced-customer-management
```

### Access Points
- **Dashboard**: http://localhost:8091/dashboard
- **API Base**: http://localhost:8091/api/v1
- **WebSocket**: ws://localhost:8091/ws

## 📡 API Endpoints

### Customer Management
```
GET    /api/v1/customers              # List customers with pagination
POST   /api/v1/customers              # Create new customer
GET    /api/v1/customers/:id          # Get customer details
PUT    /api/v1/customers/:id          # Update customer
DELETE /api/v1/customers/:id          # Delete customer
GET    /api/v1/customers/:id/profile  # Get comprehensive customer profile
GET    /api/v1/customers/:id/equipment # Get customer equipment
GET    /api/v1/customers/:id/interactions # Get customer interactions
POST   /api/v1/customers/:id/interactions # Create customer interaction
```

### Dashboard & Analytics
```
GET    /api/v1/dashboard/overview     # Dashboard overview data
GET    /api/v1/dashboard/metrics      # Customer metrics
GET    /api/v1/dashboard/recent-activity # Recent customer activity
POST   /api/v1/search/customers       # Advanced customer search
GET    /api/v1/analytics/customers    # Customer analytics
```

## 🎨 UI Features

### Polish Language Support
- Complete Polish interface
- HVAC-specific terminology
- Warsaw district names
- Polish currency (PLN) formatting

### Customer Types
- **Mieszkaniowy** - Residential customers
- **Komercyjny** - Commercial customers  
- **Przemysłowy** - Industrial customers

### HVAC Equipment Brands
- Daikin
- LG
- Mitsubishi
- Carrier
- And more...

## 🔧 Configuration

### Database Connection
```go
dsn := "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable TimeZone=Europe/Warsaw"
```

### Customer Service Configuration
```go
customerConfig := &customer.CustomerConfig{
    AutoCreateCustomers:  true,
    MatchingThreshold:   0.8,
    EnableAIEnrichment:  true,
    DefaultSatisfaction: 3.0,
    AnalyticsUpdateFreq: "daily",
}
```

## 📊 Data Models

### Customer
- UUID primary key
- Contact information (email, phone, address)
- Customer type and status
- Satisfaction scoring
- Lifetime value tracking
- AI-powered analytics

### Customer Interaction
- Interaction timeline
- Communication history
- Service visit records
- Email and phone tracking

### Customer Analytics
- Health scoring
- Churn prediction
- Upsell opportunities
- Satisfaction trends

## 🌐 Real-time Features

### WebSocket Updates
- Live customer activity
- Real-time dashboard updates
- Instant notifications
- Multi-client synchronization

### Event Types
- `customer_created`
- `customer_updated`
- `customer_deleted`
- `dashboard_update`

## 🔍 Search & Filtering

### Search Capabilities
- Full-text search across name, email, phone, company
- Advanced filtering by customer type, status, district
- Satisfaction score ranges
- Lifetime value ranges
- Date range filtering

### Sorting Options
- Creation date
- Customer name
- Satisfaction score
- Lifetime value
- Last interaction

## 📈 Analytics & Metrics

### Customer Metrics
- Total customers
- New customers (monthly)
- Active customers
- High-value customers (>10,000 PLN)
- Average satisfaction score
- Average lifetime value
- Churn rate
- Customer growth rate

### Geographic Distribution
- Warsaw district breakdown
- Customer density mapping
- Service area optimization

## 🔗 Integration Points

### Existing GoSpine Services
- CustomerIntelligenceService
- Email Intelligence
- Equipment Registry
- Service Ticket System
- AI Services (Gemma3)

### Database Integration
- PostgreSQL with GORM
- JSONB for flexible data
- Proper foreign key relationships
- Optimized queries

## 🛡️ Security Features

- CORS enabled for development
- Input validation
- SQL injection prevention
- UUID-based identifiers
- Secure database connections

## 📱 Mobile Responsive

- Mobile-first design
- Responsive grid layout
- Touch-friendly interface
- Optimized for tablets and phones

## 🎯 Next Steps

1. **Enhanced UI Components**
   - Customer detail modal
   - Equipment management interface
   - Interaction timeline visualization

2. **Advanced Analytics**
   - Predictive analytics dashboard
   - Customer segmentation analysis
   - Revenue forecasting

3. **Integration Enhancements**
   - Real-time email integration
   - Service ticket correlation
   - Equipment lifecycle tracking

4. **Performance Optimization**
   - Database query optimization
   - Caching implementation
   - WebSocket scaling

## 📞 Support

For technical support and feature requests, please refer to the main GoSpine documentation or contact the development team.

---

**Built with ❤️ for the HVAC industry in Poland** 🇵🇱
