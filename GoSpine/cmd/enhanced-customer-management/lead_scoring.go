package main

import (
	"encoding/json"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"gobackend-hvac-kratos/internal/customer"
)

// 🎯 Lead Scoring System - Inspired by LiveSpace CRM
// Automatyczna ocena potencjalnych klientów na podstawie kryteriów HVAC

// 📊 Lead Scoring Criteria
type LeadScoringCriteria struct {
	ID                    uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name                  string    `json:"name" gorm:"not null"`
	Description           string    `json:"description"`
	CustomerType          string    `json:"customer_type"` // Mieszkaniowy, Komercyjny, Przemysłowy
	BudgetMin             float64   `json:"budget_min"`
	BudgetMax             float64   `json:"budget_max"`
	DecisionLevel         string    `json:"decision_level"` // Właściciel, Menedżer, Pracownik
	CompanySize           string    `json:"company_size"`   // Mała, Średnia, Duża
	EquipmentAge          int       `json:"equipment_age"`  // Wiek urządzeń w latach
	ServiceFrequency      string    `json:"service_frequency"` // Regularna, Sporadyczna, Awaryjna
	WarsawDistrict        string    `json:"warsaw_district"`
	PreviousTransactions  int       `json:"previous_transactions"`
	ReferralSource        string    `json:"referral_source"`
	UrgencyLevel          string    `json:"urgency_level"` // Wysoka, Średnia, Niska
	SeasonalFactor        bool      `json:"seasonal_factor"` // Czy sezonowy (klimatyzacja/ogrzewanie)
	CompetitorAnalysis    string    `json:"competitor_analysis"`
	TechnicalComplexity   string    `json:"technical_complexity"` // Prosta, Średnia, Złożona
	Score                 float64   `json:"score"`
	Weight                float64   `json:"weight"`
	IsActive              bool      `json:"is_active" gorm:"default:true"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

// 🏆 Lead Score Result
type LeadScoreResult struct {
	CustomerID       uuid.UUID                `json:"customer_id"`
	TotalScore       float64                  `json:"total_score"`
	MaxPossibleScore float64                  `json:"max_possible_score"`
	ScorePercentage  float64                  `json:"score_percentage"`
	ScoreGrade       string                   `json:"score_grade"` // A, B, C, D
	Priority         string                   `json:"priority"`    // Wysoki, Średni, Niski
	Recommendations  []string                 `json:"recommendations"`
	CriteriaScores   []CriteriaScore          `json:"criteria_scores"`
	CalculatedAt     time.Time                `json:"calculated_at"`
	NextReviewDate   time.Time                `json:"next_review_date"`
	AutoAssignment   *AutoAssignmentResult    `json:"auto_assignment,omitempty"`
}

// 📋 Criteria Score Detail
type CriteriaScore struct {
	CriteriaID   uuid.UUID `json:"criteria_id"`
	CriteriaName string    `json:"criteria_name"`
	Score        float64   `json:"score"`
	Weight       float64   `json:"weight"`
	WeightedScore float64  `json:"weighted_score"`
	Explanation  string    `json:"explanation"`
}

// 🤖 Auto Assignment Result (Sales Bot functionality)
type AutoAssignmentResult struct {
	AssignedTo       string    `json:"assigned_to"`
	AssignmentReason string    `json:"assignment_reason"`
	AssignedAt       time.Time `json:"assigned_at"`
	ExpectedResponse time.Time `json:"expected_response"`
}

// 🎯 Lead Scoring Service
type LeadScoringService struct {
	criteria []LeadScoringCriteria
}

// Initialize default HVAC lead scoring criteria
func NewLeadScoringService() *LeadScoringService {
	service := &LeadScoringService{}
	service.initializeDefaultCriteria()
	return service
}

func (ls *LeadScoringService) initializeDefaultCriteria() {
	ls.criteria = []LeadScoringCriteria{
		{
			ID:                  uuid.New(),
			Name:                "Typ Klienta - Komercyjny",
			Description:         "Klienci komercyjni mają wyższy potencjał przychodowy",
			CustomerType:        "commercial",
			Score:               25.0,
			Weight:              1.5,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Budżet - Wysoki",
			Description:         "Klienci z budżetem powyżej 50,000 PLN",
			BudgetMin:           50000.0,
			Score:               20.0,
			Weight:              1.3,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Poziom Decyzyjny - Właściciel",
			Description:         "Bezpośredni kontakt z decydentem",
			DecisionLevel:       "owner",
			Score:               15.0,
			Weight:              1.2,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Lokalizacja - Warszawa Centrum",
			Description:         "Klienci z dzielnic centralnych Warszawy",
			WarsawDistrict:      "Śródmieście",
			Score:               10.0,
			Weight:              1.1,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Wiek Urządzeń - Stare",
			Description:         "Urządzenia starsze niż 10 lat wymagają wymiany",
			EquipmentAge:        10,
			Score:               15.0,
			Weight:              1.2,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Pilność - Wysoka",
			Description:         "Awarie wymagające natychmiastowej interwencji",
			UrgencyLevel:        "high",
			Score:               20.0,
			Weight:              1.4,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Czynnik Sezonowy",
			Description:         "Zapotrzebowanie sezonowe na klimatyzację/ogrzewanie",
			SeasonalFactor:      true,
			Score:               8.0,
			Weight:              1.0,
			IsActive:            true,
		},
		{
			ID:                  uuid.New(),
			Name:                "Historia Transakcji",
			Description:         "Klienci z historią współpracy",
			PreviousTransactions: 1,
			Score:               12.0,
			Weight:              1.1,
			IsActive:            true,
		},
	}
}

// 🎯 Calculate Lead Score for Customer
func (ls *LeadScoringService) CalculateLeadScore(customer *customer.Customer) *LeadScoreResult {
	result := &LeadScoreResult{
		CustomerID:      customer.ID,
		CriteriaScores:  make([]CriteriaScore, 0),
		Recommendations: make([]string, 0),
		CalculatedAt:    time.Now(),
		NextReviewDate:  time.Now().AddDate(0, 1, 0), // Review monthly
	}

	totalScore := 0.0
	maxPossibleScore := 0.0

	// Evaluate each criteria
	for _, criteria := range ls.criteria {
		if !criteria.IsActive {
			continue
		}

		score := ls.evaluateCriteria(customer, criteria)
		weightedScore := score * criteria.Weight
		
		criteriaScore := CriteriaScore{
			CriteriaID:    criteria.ID,
			CriteriaName:  criteria.Name,
			Score:         score,
			Weight:        criteria.Weight,
			WeightedScore: weightedScore,
			Explanation:   ls.getScoreExplanation(customer, criteria, score),
		}

		result.CriteriaScores = append(result.CriteriaScores, criteriaScore)
		totalScore += weightedScore
		maxPossibleScore += criteria.Score * criteria.Weight
	}

	result.TotalScore = totalScore
	result.MaxPossibleScore = maxPossibleScore
	result.ScorePercentage = (totalScore / maxPossibleScore) * 100

	// Assign grade and priority
	result.ScoreGrade = ls.calculateGrade(result.ScorePercentage)
	result.Priority = ls.calculatePriority(result.ScorePercentage)

	// Generate recommendations
	result.Recommendations = ls.generateRecommendations(customer, result)

	// Auto-assignment (Sales Bot functionality)
	result.AutoAssignment = ls.calculateAutoAssignment(customer, result)

	return result
}

// 📊 Evaluate individual criteria
func (ls *LeadScoringService) evaluateCriteria(customer *customer.Customer, criteria LeadScoringCriteria) float64 {
	score := 0.0

	// Customer Type evaluation
	if criteria.CustomerType != "" && customer.CustomerType == criteria.CustomerType {
		score += criteria.Score
	}

	// Budget evaluation
	if criteria.BudgetMin > 0 && customer.LifetimeValue >= criteria.BudgetMin {
		score += criteria.Score
	}

	// Decision Level evaluation
	if criteria.DecisionLevel != "" {
		// This would need to be added to customer model
		// For now, assume based on customer type
		if customer.CustomerType == "commercial" && criteria.DecisionLevel == "owner" {
			score += criteria.Score * 0.8 // Partial score
		}
	}

	// Warsaw District evaluation
	if criteria.WarsawDistrict != "" {
		// This would need address parsing
		// For now, simplified check
		if customer.Address != nil {
			addressData, _ := json.Marshal(customer.Address)
			if string(addressData) != "" {
				score += criteria.Score * 0.5 // Partial score for having address
			}
		}
	}

	// Previous Transactions evaluation
	if criteria.PreviousTransactions > 0 && customer.LifetimeValue > 0 {
		score += criteria.Score
	}

	// Urgency Level evaluation
	if criteria.UrgencyLevel == "high" && customer.SatisfactionScore < 3.0 {
		score += criteria.Score // Low satisfaction = high urgency
	}

	// Seasonal Factor evaluation
	if criteria.SeasonalFactor {
		currentMonth := time.Now().Month()
		// Summer (AC) and Winter (Heating) seasons
		if currentMonth >= 6 && currentMonth <= 8 || currentMonth >= 12 || currentMonth <= 2 {
			score += criteria.Score
		}
	}

	return score
}

// 📝 Generate score explanation
func (ls *LeadScoringService) getScoreExplanation(customer *customer.Customer, criteria LeadScoringCriteria, score float64) string {
	if score == 0 {
		return "Kryterium nie zostało spełnione"
	}
	
	if score == criteria.Score {
		return "Kryterium w pełni spełnione"
	}
	
	return "Kryterium częściowo spełnione"
}

// 🏆 Calculate grade based on score percentage
func (ls *LeadScoringService) calculateGrade(percentage float64) string {
	if percentage >= 80 {
		return "A"
	} else if percentage >= 60 {
		return "B"
	} else if percentage >= 40 {
		return "C"
	}
	return "D"
}

// ⚡ Calculate priority based on score percentage
func (ls *LeadScoringService) calculatePriority(percentage float64) string {
	if percentage >= 70 {
		return "Wysoki"
	} else if percentage >= 40 {
		return "Średni"
	}
	return "Niski"
}

// 💡 Generate recommendations based on score
func (ls *LeadScoringService) generateRecommendations(customer *customer.Customer, result *LeadScoreResult) []string {
	recommendations := make([]string, 0)

	if result.ScorePercentage >= 80 {
		recommendations = append(recommendations, "🔥 Klient VIP - natychmiastowy kontakt!")
		recommendations = append(recommendations, "📞 Przydziel najlepszego specjalistę")
		recommendations = append(recommendations, "🎯 Przygotuj spersonalizowaną ofertę")
	} else if result.ScorePercentage >= 60 {
		recommendations = append(recommendations, "⭐ Klient o wysokim potencjale")
		recommendations = append(recommendations, "📧 Wyślij szczegółowe informacje o usługach")
		recommendations = append(recommendations, "📅 Zaplanuj wizytę w ciągu 2 dni")
	} else if result.ScorePercentage >= 40 {
		recommendations = append(recommendations, "📋 Klient standardowy")
		recommendations = append(recommendations, "📞 Kontakt w ciągu tygodnia")
		recommendations = append(recommendations, "📄 Wyślij standardową ofertę")
	} else {
		recommendations = append(recommendations, "⏳ Klient o niskim priorytecie")
		recommendations = append(recommendations, "📧 Dodaj do kampanii nurturing")
		recommendations = append(recommendations, "📊 Monitoruj zmiany w profilu")
	}

	// HVAC-specific recommendations
	if customer.CustomerType == "commercial" {
		recommendations = append(recommendations, "🏢 Zaproponuj kontrakt serwisowy")
	}

	if customer.SatisfactionScore < 3.0 {
		recommendations = append(recommendations, "⚠️ Klient niezadowolony - priorytetowy kontakt")
	}

	return recommendations
}

// 🤖 Calculate auto-assignment (Sales Bot functionality)
func (ls *LeadScoringService) calculateAutoAssignment(customer *customer.Customer, result *LeadScoreResult) *AutoAssignmentResult {
	assignment := &AutoAssignmentResult{
		AssignedAt: time.Now(),
	}

	// Assignment logic based on score and customer type
	if result.ScorePercentage >= 80 {
		assignment.AssignedTo = "senior_specialist"
		assignment.AssignmentReason = "Klient VIP - wymaga doświadczonego specjalisty"
		assignment.ExpectedResponse = time.Now().Add(1 * time.Hour)
	} else if result.ScorePercentage >= 60 {
		assignment.AssignedTo = "specialist"
		assignment.AssignmentReason = "Klient o wysokim potencjale"
		assignment.ExpectedResponse = time.Now().Add(4 * time.Hour)
	} else if customer.CustomerType == "commercial" {
		assignment.AssignedTo = "commercial_specialist"
		assignment.AssignmentReason = "Klient komercyjny - specjalista B2B"
		assignment.ExpectedResponse = time.Now().Add(8 * time.Hour)
	} else {
		assignment.AssignedTo = "junior_specialist"
		assignment.AssignmentReason = "Standardowy klient mieszkaniowy"
		assignment.ExpectedResponse = time.Now().Add(24 * time.Hour)
	}

	return assignment
}

// 🎯 API Handler for Lead Scoring
func (s *CustomerManagementServer) handleCalculateLeadScore(c *gin.Context) {
	customerID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(400, gin.H{"error": "Invalid customer ID"})
		return
	}

	var customer customer.Customer
	err = s.db.First(&customer, customerID).Error
	if err != nil {
		c.JSON(404, gin.H{"error": "Customer not found"})
		return
	}

	leadScoringService := NewLeadScoringService()
	scoreResult := leadScoringService.CalculateLeadScore(&customer)

	c.JSON(200, scoreResult)
}

// 📊 API Handler for Lead Scoring Criteria
func (s *CustomerManagementServer) handleGetLeadScoringCriteria(c *gin.Context) {
	leadScoringService := NewLeadScoringService()
	c.JSON(200, gin.H{
		"criteria": leadScoringService.criteria,
		"total_criteria": len(leadScoringService.criteria),
	})
}

// 🎯 API Handler for Bulk Lead Scoring
func (s *CustomerManagementServer) handleBulkLeadScoring(c *gin.Context) {
	var customers []*customer.Customer
	err := s.db.Where("status = ?", "active").Find(&customers).Error
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	leadScoringService := NewLeadScoringService()
	results := make([]*LeadScoreResult, 0)

	for _, customer := range customers {
		scoreResult := leadScoringService.CalculateLeadScore(customer)
		results = append(results, scoreResult)
	}

	c.JSON(200, gin.H{
		"results": results,
		"total_scored": len(results),
		"timestamp": time.Now(),
	})
}
