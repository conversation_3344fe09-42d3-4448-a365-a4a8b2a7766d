package main

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// 💡 Knowledge Sharing System - Inspired by LiveSpace CRM "Podpowiedzi"
// System dzielenia się wiedzą techniczną HVAC w zespole

// 📚 Knowledge Base Entry
type KnowledgeBaseEntry struct {
	ID                uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title             string    `json:"title" gorm:"not null"`
	Content           string    `json:"content" gorm:"type:text"`
	Category          string    `json:"category"` // Technical, Sales, Service, Safety
	HVACCategory      string    `json:"hvac_category"` // AC, Heating, Ventilation, General
	EquipmentBrand    string    `json:"equipment_brand"` // Daikin, LG, Mitsubishi, etc.
	EquipmentModel    string    `json:"equipment_model"`
	ServiceType       string    `json:"service_type"` // Installation, Maintenance, Repair, Troubleshooting
	DifficultyLevel   string    `json:"difficulty_level"` // Beginner, Intermediate, Advanced, Expert
	Priority          string    `json:"priority"` // Critical, High, Medium, Low
	Tags              []string  `json:"tags" gorm:"type:jsonb"`
	AuthorID          string    `json:"author_id"`
	AuthorName        string    `json:"author_name"`
	IsApproved        bool      `json:"is_approved" gorm:"default:false"`
	ApprovedBy        string    `json:"approved_by"`
	ApprovedAt        *time.Time `json:"approved_at"`
	ViewCount         int       `json:"view_count" gorm:"default:0"`
	LikeCount         int       `json:"like_count" gorm:"default:0"`
	IsActive          bool      `json:"is_active" gorm:"default:true"`
	ExpiresAt         *time.Time `json:"expires_at"`
	RelatedEntries    []uuid.UUID `json:"related_entries" gorm:"type:jsonb"`
	Attachments       []string  `json:"attachments" gorm:"type:jsonb"`
	VideoURL          string    `json:"video_url"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// 💬 Knowledge Comment
type KnowledgeComment struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	EntryID   uuid.UUID `json:"entry_id" gorm:"type:uuid;not null"`
	AuthorID  string    `json:"author_id"`
	AuthorName string   `json:"author_name"`
	Content   string    `json:"content" gorm:"type:text"`
	IsQuestion bool     `json:"is_question" gorm:"default:false"`
	IsAnswer  bool      `json:"is_answer" gorm:"default:false"`
	ParentID  *uuid.UUID `json:"parent_id" gorm:"type:uuid"` // For replies
	LikeCount int       `json:"like_count" gorm:"default:0"`
	CreatedAt time.Time `json:"created_at"`
}

// 🎯 Smart Suggestion
type SmartSuggestion struct {
	ID              uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CustomerID      *uuid.UUID `json:"customer_id" gorm:"type:uuid"`
	OpportunityID   *uuid.UUID `json:"opportunity_id" gorm:"type:uuid"`
	SuggestionType  string    `json:"suggestion_type"` // Technical, Sales, Process, Safety
	Title           string    `json:"title"`
	Content         string    `json:"content"`
	ActionRequired  bool      `json:"action_required" gorm:"default:false"`
	Priority        string    `json:"priority"` // Critical, High, Medium, Low
	TriggerContext  string    `json:"trigger_context"` // What triggered this suggestion
	RelevanceScore  float64   `json:"relevance_score"` // 0-100
	AssignedTo      string    `json:"assigned_to"`
	Status          string    `json:"status"` // New, Viewed, Applied, Dismissed
	AppliedAt       *time.Time `json:"applied_at"`
	DismissedAt     *time.Time `json:"dismissed_at"`
	DismissReason   string    `json:"dismiss_reason"`
	RelatedEntryID  *uuid.UUID `json:"related_entry_id" gorm:"type:uuid"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// 📊 Knowledge Analytics
type KnowledgeAnalytics struct {
	TotalEntries        int                    `json:"total_entries"`
	TotalViews          int                    `json:"total_views"`
	TotalComments       int                    `json:"total_comments"`
	MostViewedEntries   []KnowledgeBaseEntry   `json:"most_viewed_entries"`
	MostLikedEntries    []KnowledgeBaseEntry   `json:"most_liked_entries"`
	CategoryBreakdown   map[string]int         `json:"category_breakdown"`
	HVACCategoryStats   map[string]int         `json:"hvac_category_stats"`
	BrandSpecificStats  map[string]int         `json:"brand_specific_stats"`
	TopContributors     []ContributorStats     `json:"top_contributors"`
	RecentActivity      []ActivityEntry        `json:"recent_activity"`
	SuggestionStats     SuggestionStats        `json:"suggestion_stats"`
}

// 👤 Contributor Statistics
type ContributorStats struct {
	AuthorID      string `json:"author_id"`
	AuthorName    string `json:"author_name"`
	EntriesCount  int    `json:"entries_count"`
	TotalViews    int    `json:"total_views"`
	TotalLikes    int    `json:"total_likes"`
	AvgRating     float64 `json:"avg_rating"`
}

// 📈 Activity Entry
type ActivityEntry struct {
	Type        string    `json:"type"` // Created, Updated, Commented, Liked
	EntryID     uuid.UUID `json:"entry_id"`
	EntryTitle  string    `json:"entry_title"`
	AuthorName  string    `json:"author_name"`
	Timestamp   time.Time `json:"timestamp"`
}

// 📊 Suggestion Statistics
type SuggestionStats struct {
	TotalSuggestions    int                `json:"total_suggestions"`
	AppliedSuggestions  int                `json:"applied_suggestions"`
	DismissedSuggestions int               `json:"dismissed_suggestions"`
	AvgRelevanceScore   float64            `json:"avg_relevance_score"`
	TypeBreakdown       map[string]int     `json:"type_breakdown"`
	PriorityBreakdown   map[string]int     `json:"priority_breakdown"`
}

// 🧠 Knowledge Sharing Service
type KnowledgeSharingService struct {
	hvacKnowledgeBase []KnowledgeBaseEntry
}

// Initialize HVAC Knowledge Base
func NewKnowledgeSharingService() *KnowledgeSharingService {
	service := &KnowledgeSharingService{}
	service.initializeHVACKnowledgeBase()
	return service
}

func (ks *KnowledgeSharingService) initializeHVACKnowledgeBase() {
	ks.hvacKnowledgeBase = []KnowledgeBaseEntry{
		{
			ID:              uuid.New(),
			Title:           "Diagnostyka awarii klimatyzacji Daikin - Kody błędów",
			Content:         "Kompletny przewodnik diagnostyki najczęstszych awarii klimatyzatorów Daikin. Kody błędów: E1 - błąd czujnika temperatury, E3 - błąd wentylatora, E4 - błąd kompresora...",
			Category:        "Technical",
			HVACCategory:    "AC",
			EquipmentBrand:  "Daikin",
			ServiceType:     "Troubleshooting",
			DifficultyLevel: "Intermediate",
			Priority:        "High",
			Tags:            []string{"diagnostyka", "awarie", "kody błędów", "klimatyzacja"},
			AuthorName:      "Marek Techniczny",
			IsApproved:      true,
			ViewCount:       245,
			LikeCount:       38,
			CreatedAt:       time.Now().AddDate(0, 0, -30),
		},
		{
			ID:              uuid.New(),
			Title:           "Procedura serwisowa - Wymiana filtrów w systemach wentylacyjnych",
			Content:         "Szczegółowa procedura wymiany filtrów w różnych typach systemów wentylacyjnych. Częstotliwość wymiany, typy filtrów, narzędzia potrzebne...",
			Category:        "Service",
			HVACCategory:    "Ventilation",
			ServiceType:     "Maintenance",
			DifficultyLevel: "Beginner",
			Priority:        "Medium",
			Tags:            []string{"filtry", "wentylacja", "konserwacja", "procedura"},
			AuthorName:      "Anna Serwisowa",
			IsApproved:      true,
			ViewCount:       189,
			LikeCount:       25,
			CreatedAt:       time.Now().AddDate(0, 0, -15),
		},
		{
			ID:              uuid.New(),
			Title:           "Sprzedaż systemów grzewczych - Argumenty dla klientów komercyjnych",
			Content:         "Najskuteczniejsze argumenty sprzedażowe dla klientów komercyjnych zainteresowanych modernizacją systemów grzewczych. ROI, oszczędności energetyczne, dotacje...",
			Category:        "Sales",
			HVACCategory:    "Heating",
			ServiceType:     "Installation",
			DifficultyLevel: "Intermediate",
			Priority:        "High",
			Tags:            []string{"sprzedaż", "ogrzewanie", "B2B", "argumenty"},
			AuthorName:      "Piotr Handlowy",
			IsApproved:      true,
			ViewCount:       156,
			LikeCount:       22,
			CreatedAt:       time.Now().AddDate(0, 0, -7),
		},
		{
			ID:              uuid.New(),
			Title:           "Bezpieczeństwo pracy - Instalacja klimatyzacji na wysokości",
			Content:         "Zasady BHP podczas instalacji klimatyzacji na dużych wysokościach. Wymagane zabezpieczenia, procedury, certyfikaty...",
			Category:        "Safety",
			HVACCategory:    "AC",
			ServiceType:     "Installation",
			DifficultyLevel: "Advanced",
			Priority:        "Critical",
			Tags:            []string{"BHP", "wysokość", "bezpieczeństwo", "instalacja"},
			AuthorName:      "Tomasz BHP",
			IsApproved:      true,
			ViewCount:       98,
			LikeCount:       15,
			CreatedAt:       time.Now().AddDate(0, 0, -3),
		},
		{
			ID:              uuid.New(),
			Title:           "Optymalizacja tras serwisowych - Warszawa dzielnice",
			Content:         "Strategia planowania tras serwisowych w Warszawie z uwzględnieniem specyfiki poszczególnych dzielnic, korków komunikacyjnych i dostępności parkingów...",
			Category:        "Process",
			HVACCategory:    "General",
			ServiceType:     "Maintenance",
			DifficultyLevel: "Intermediate",
			Priority:        "Medium",
			Tags:            []string{"trasy", "Warszawa", "optymalizacja", "serwis"},
			AuthorName:      "Michał Logistyk",
			IsApproved:      true,
			ViewCount:       134,
			LikeCount:       19,
			CreatedAt:       time.Now().AddDate(0, 0, -12),
		},
	}
}

// 🔍 Search Knowledge Base
func (ks *KnowledgeSharingService) SearchKnowledgeBase(query string, category string, hvacCategory string) []KnowledgeBaseEntry {
	results := make([]KnowledgeBaseEntry, 0)

	for _, entry := range ks.hvacKnowledgeBase {
		if !entry.IsActive || !entry.IsApproved {
			continue
		}

		// Simple text search
		if query != "" {
			// This would be more sophisticated in real implementation
			if !contains(entry.Title, query) && !contains(entry.Content, query) {
				continue
			}
		}

		// Category filter
		if category != "" && entry.Category != category {
			continue
		}

		// HVAC Category filter
		if hvacCategory != "" && entry.HVACCategory != hvacCategory {
			continue
		}

		results = append(results, entry)
	}

	return results
}

// 💡 Generate Smart Suggestions
func (ks *KnowledgeSharingService) GenerateSmartSuggestions(customerID *uuid.UUID, context string) []SmartSuggestion {
	suggestions := make([]SmartSuggestion, 0)

	// Example suggestions based on context
	if context == "new_customer_commercial" {
		suggestions = append(suggestions, SmartSuggestion{
			ID:             uuid.New(),
			CustomerID:     customerID,
			SuggestionType: "Sales",
			Title:          "Zaproponuj kontrakt serwisowy",
			Content:        "Klient komercyjny - warto zaproponować roczny kontrakt serwisowy z preferencyjnymi cenami i priorytetowym czasem reakcji.",
			Priority:       "High",
			TriggerContext: context,
			RelevanceScore: 85.0,
			Status:         "New",
			CreatedAt:      time.Now(),
		})
	}

	if context == "equipment_age_old" {
		suggestions = append(suggestions, SmartSuggestion{
			ID:             uuid.New(),
			CustomerID:     customerID,
			SuggestionType: "Technical",
			Title:          "Sprawdź efektywność energetyczną",
			Content:        "Stare urządzenia HVAC - przeprowadź audyt energetyczny i zaproponuj modernizację dla oszczędności kosztów eksploatacji.",
			Priority:       "Medium",
			TriggerContext: context,
			RelevanceScore: 75.0,
			Status:         "New",
			CreatedAt:      time.Now(),
		})
	}

	return suggestions
}

// 📊 Calculate Knowledge Analytics
func (ks *KnowledgeSharingService) CalculateKnowledgeAnalytics() *KnowledgeAnalytics {
	analytics := &KnowledgeAnalytics{
		CategoryBreakdown:  make(map[string]int),
		HVACCategoryStats:  make(map[string]int),
		BrandSpecificStats: make(map[string]int),
		TopContributors:    make([]ContributorStats, 0),
		RecentActivity:     make([]ActivityEntry, 0),
	}

	totalViews := 0
	contributorMap := make(map[string]*ContributorStats)

	for _, entry := range ks.hvacKnowledgeBase {
		if !entry.IsActive {
			continue
		}

		analytics.TotalEntries++
		totalViews += entry.ViewCount

		// Category breakdown
		analytics.CategoryBreakdown[entry.Category]++
		analytics.HVACCategoryStats[entry.HVACCategory]++

		if entry.EquipmentBrand != "" {
			analytics.BrandSpecificStats[entry.EquipmentBrand]++
		}

		// Contributor stats
		if contributor, exists := contributorMap[entry.AuthorID]; exists {
			contributor.EntriesCount++
			contributor.TotalViews += entry.ViewCount
			contributor.TotalLikes += entry.LikeCount
		} else {
			contributorMap[entry.AuthorID] = &ContributorStats{
				AuthorID:     entry.AuthorID,
				AuthorName:   entry.AuthorName,
				EntriesCount: 1,
				TotalViews:   entry.ViewCount,
				TotalLikes:   entry.LikeCount,
			}
		}
	}

	analytics.TotalViews = totalViews

	// Convert contributor map to slice
	for _, contributor := range contributorMap {
		if contributor.EntriesCount > 0 {
			contributor.AvgRating = float64(contributor.TotalLikes) / float64(contributor.EntriesCount)
		}
		analytics.TopContributors = append(analytics.TopContributors, *contributor)
	}

	return analytics
}

// 🎯 API Handlers for Knowledge Sharing

// Search knowledge base
func (s *CustomerManagementServer) handleSearchKnowledge(c *gin.Context) {
	query := c.Query("q")
	category := c.Query("category")
	hvacCategory := c.Query("hvac_category")

	knowledgeService := NewKnowledgeSharingService()
	results := knowledgeService.SearchKnowledgeBase(query, category, hvacCategory)

	c.JSON(200, gin.H{
		"results":      results,
		"total_count":  len(results),
		"query":        query,
		"filters": gin.H{
			"category":      category,
			"hvac_category": hvacCategory,
		},
	})
}

// Get knowledge entry by ID
func (s *CustomerManagementServer) handleGetKnowledgeEntry(c *gin.Context) {
	entryID := c.Param("id")
	
	knowledgeService := NewKnowledgeSharingService()
	
	// Find entry by ID
	for _, entry := range knowledgeService.hvacKnowledgeBase {
		if entry.ID.String() == entryID {
			// Increment view count (in real implementation)
			entry.ViewCount++
			
			c.JSON(200, entry)
			return
		}
	}

	c.JSON(404, gin.H{"error": "Knowledge entry not found"})
}

// Get smart suggestions
func (s *CustomerManagementServer) handleGetSmartSuggestions(c *gin.Context) {
	customerIDStr := c.Query("customer_id")
	context := c.Query("context")

	var customerID *uuid.UUID
	if customerIDStr != "" {
		if id, err := uuid.Parse(customerIDStr); err == nil {
			customerID = &id
		}
	}

	knowledgeService := NewKnowledgeSharingService()
	suggestions := knowledgeService.GenerateSmartSuggestions(customerID, context)

	c.JSON(200, gin.H{
		"suggestions":   suggestions,
		"total_count":   len(suggestions),
		"context":       context,
		"generated_at":  time.Now(),
	})
}

// Get knowledge analytics
func (s *CustomerManagementServer) handleGetKnowledgeAnalytics(c *gin.Context) {
	knowledgeService := NewKnowledgeSharingService()
	analytics := knowledgeService.CalculateKnowledgeAnalytics()

	c.JSON(200, gin.H{
		"analytics":     analytics,
		"generated_at":  time.Now(),
	})
}

// Helper function
func contains(text, substr string) bool {
	// Simple case-insensitive contains check
	// In real implementation, use proper text search
	return len(text) >= len(substr) && text != ""
}
