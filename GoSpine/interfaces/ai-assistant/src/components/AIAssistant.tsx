import React, { useState, useRef, useEffect } from 'react';
import { 
  MicrophoneIcon, 
  StopIcon, 
  PaperAirplaneIcon,
  CameraIcon,
  DocumentTextIcon,
  WrenchScrewdriverIcon,
  LightBulbIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    confidence?: number;
    sources?: string[];
    attachments?: string[];
  };
}

interface AIAssistantProps {
  onMessage?: (message: string) => void;
  onVoiceCommand?: (command: string) => void;
  onImageAnalysis?: (image: File) => void;
}

export function AIAssistant({ onMessage, onVoiceCommand, onImageAnalysis }: AIAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: '<PERSON><PERSON><PERSON>ć! Jestem GoSpine AI Assistant. Jak mogę <PERSON> pomóc dzisiaj? <PERSON><PERSON><PERSON><PERSON> zadać mi pytanie o HVAC, pop<PERSON><PERSON> o diagnostykę problemu, lub zapyta<PERSON> o specyfikacje techniczne.',
      timestamp: new Date(),
      metadata: { confidence: 1.0 }
    }
  ]);
  
  const [inputValue, setInputValue] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [voiceSupported, setVoiceSupported] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const recognition = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    // Check for speech recognition support
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setVoiceSupported(true);
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'pl-PL';

      recognition.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceInput(transcript);
      };

      recognition.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(inputValue);
      setMessages(prev => [...prev, aiResponse]);
      setIsProcessing(false);
    }, 1500);

    onMessage?.(inputValue);
  };

  const handleVoiceInput = (transcript: string) => {
    setInputValue(transcript);
    onVoiceCommand?.(transcript);
  };

  const startListening = () => {
    if (recognition.current && voiceSupported) {
      setIsListening(true);
      recognition.current.start();
    }
  };

  const stopListening = () => {
    if (recognition.current) {
      recognition.current.stop();
      setIsListening(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImageAnalysis?.(file);
      
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: `Przesłano zdjęcie: ${file.name}`,
        timestamp: new Date(),
        metadata: { attachments: [file.name] }
      };

      setMessages(prev => [...prev, userMessage]);
      
      // Simulate image analysis
      setTimeout(() => {
        const analysisResponse: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: 'Analizuję przesłane zdjęcie... Widzę jednostkę klimatyzacyjną marki Carrier. Na podstawie obrazu mogę zidentyfikować model 24ABC6. Czy masz konkretny problem z tym urządzeniem?',
          timestamp: new Date(),
          metadata: { confidence: 0.92, sources: ['Computer Vision Analysis', 'Equipment Database'] }
        };
        setMessages(prev => [...prev, analysisResponse]);
      }, 2000);
    }
  };

  const generateAIResponse = (input: string): Message => {
    const lowerInput = input.toLowerCase();
    
    let response = '';
    let confidence = 0.9;
    let sources = ['HVAC Knowledge Base'];

    if (lowerInput.includes('klimatyzacja') || lowerInput.includes('ac')) {
      response = 'Rozumiem, że masz pytanie dotyczące klimatyzacji. Czy możesz opisać konkretny problem? Na przykład: czy urządzenie nie chłodzi, wydaje nietypowe dźwięki, czy może masz problem z filtrem?';
      sources.push('AC Troubleshooting Guide');
    } else if (lowerInput.includes('ogrzewanie') || lowerInput.includes('heating')) {
      response = 'Pomogę Ci z systemem ogrzewania. Jaki typ systemu masz zainstalowany? Czy to pompa ciepła, kocioł gazowy, czy może ogrzewanie elektryczne?';
      sources.push('Heating Systems Manual');
    } else if (lowerInput.includes('wentylacja') || lowerInput.includes('ventilation')) {
      response = 'Wentylacja to kluczowy element systemu HVAC. Czy problem dotyczy przepływu powietrza, hałasu, czy może jakości powietrza? Mogę pomóc w diagnostyce i doborze odpowiednich rozwiązań.';
      sources.push('Ventilation Standards');
    } else if (lowerInput.includes('błąd') || lowerInput.includes('error')) {
      response = 'Widzę, że masz problem z błędem systemu. Czy możesz podać kod błędu lub opisać objawy? Pomogę Ci zdiagnozować problem i zaproponować rozwiązanie.';
      sources.push('Error Code Database');
    } else {
      response = 'Dziękuję za pytanie. Czy możesz podać więcej szczegółów? Im więcej informacji otrzymam, tym lepiej będę mógł Ci pomóc. Możesz również przesłać zdjęcie urządzenia lub opisać konkretne objawy.';
      confidence = 0.7;
    }

    return {
      id: Date.now().toString(),
      type: 'assistant',
      content: response,
      timestamp: new Date(),
      metadata: { confidence, sources }
    };
  };

  const getMessageIcon = (message: Message) => {
    if (message.type === 'user') return null;
    
    const content = message.content.toLowerCase();
    if (content.includes('błąd') || content.includes('problem')) {
      return <ExclamationTriangleIcon className="h-4 w-4 text-hvac-red-500" />;
    } else if (content.includes('rozwiązanie') || content.includes('pomoc')) {
      return <LightBulbIcon className="h-4 w-4 text-hvac-green-500" />;
    } else if (content.includes('narzędzi') || content.includes('części')) {
      return <WrenchScrewdriverIcon className="h-4 w-4 text-hvac-blue-500" />;
    }
    return <DocumentTextIcon className="h-4 w-4 text-hvac-blue-500" />;
  };

  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-hvac-lg border border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-hvac-blue-600 to-hvac-green-600">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
            <span className="text-hvac-blue-600 font-bold text-sm">AI</span>
          </div>
          <div>
            <h3 className="text-white font-semibold">GoSpine AI Assistant</h3>
            <p className="text-hvac-blue-100 text-xs">Inteligentny asystent HVAC</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {isProcessing && (
            <div className="w-2 h-2 bg-hvac-green-400 rounded-full animate-pulse"></div>
          )}
          <span className="text-hvac-blue-100 text-xs">Online</span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.type === 'user'
                  ? 'bg-hvac-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="flex items-start space-x-2">
                  {message.type === 'assistant' && getMessageIcon(message)}
                  <div className="flex-1">
                    <p className="text-sm">{message.content}</p>
                    {message.metadata && (
                      <div className="mt-2 text-xs opacity-75">
                        {message.metadata.confidence && (
                          <div className="flex items-center space-x-1">
                            <span>Pewność:</span>
                            <div className="w-16 h-1 bg-gray-300 rounded">
                              <div 
                                className="h-1 bg-hvac-green-500 rounded"
                                style={{ width: `${message.metadata.confidence * 100}%` }}
                              ></div>
                            </div>
                            <span>{Math.round(message.metadata.confidence * 100)}%</span>
                          </div>
                        )}
                        {message.metadata.sources && (
                          <div className="mt-1">
                            <span>Źródła: {message.metadata.sources.join(', ')}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {isProcessing && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-start"
          >
            <div className="bg-gray-100 rounded-lg px-4 py-2">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-hvac-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-hvac-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-hvac-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-sm text-gray-600">AI myśli...</span>
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Zadaj pytanie o HVAC, opisz problem lub poproś o pomoc..."
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-hvac-blue-500 focus:border-transparent"
              disabled={isProcessing}
            />
          </div>
          
          {/* Voice Button */}
          {voiceSupported && (
            <button
              onClick={isListening ? stopListening : startListening}
              className={`p-2 rounded-lg transition-colors ${
                isListening 
                  ? 'bg-hvac-red-500 text-white' 
                  : 'bg-hvac-blue-500 text-white hover:bg-hvac-blue-600'
              }`}
              disabled={isProcessing}
            >
              {isListening ? (
                <StopIcon className="h-5 w-5" />
              ) : (
                <MicrophoneIcon className="h-5 w-5" />
              )}
            </button>
          )}
          
          {/* Camera Button */}
          <button
            onClick={() => fileInputRef.current?.click()}
            className="p-2 bg-hvac-green-500 text-white rounded-lg hover:bg-hvac-green-600 transition-colors"
            disabled={isProcessing}
          >
            <CameraIcon className="h-5 w-5" />
          </button>
          
          {/* Send Button */}
          <button
            onClick={handleSendMessage}
            className="p-2 bg-hvac-blue-500 text-white rounded-lg hover:bg-hvac-blue-600 transition-colors disabled:opacity-50"
            disabled={!inputValue.trim() || isProcessing}
          >
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
        
        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
        
        {/* Quick Actions */}
        <div className="mt-3 flex flex-wrap gap-2">
          <button
            onClick={() => setInputValue('Jak zdiagnozować problem z klimatyzacją?')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            Diagnostyka AC
          </button>
          <button
            onClick={() => setInputValue('Oblicz obciążenie cieplne dla pomieszczenia')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            Obliczenia HVAC
          </button>
          <button
            onClick={() => setInputValue('Pokaż schemat instalacji')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            Schematy
          </button>
          <button
            onClick={() => setInputValue('Części zamienne do urządzenia')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
          >
            Części zamienne
          </button>
        </div>
      </div>
    </div>
  );
}
